import React, { useState } from 'react';
import { GameInfo } from '../preload';
import GameCard from './GameCard';
import AddGameForm from './AddGameForm';

interface GameLibraryProps {
  games: GameInfo[];
  onAddGame: (gameInfo: Omit<GameInfo, 'id'>) => Promise<boolean>;
  onRemoveGame: (id: string) => Promise<boolean>;
  onLaunchGame: (id: string) => Promise<boolean>;
  error: string | null;
  onClearError: () => void;
}

const GameLibrary: React.FC<GameLibraryProps> = ({
  games,
  onAddGame,
  onRemoveGame,
  onLaunchGame,
  error,
  onClearError
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);

  const handleAddGame = async (gameInfo: Omit<GameInfo, 'id'>) => {
    const success = await onAddGame(gameInfo);
    if (success) {
      setShowAddForm(false);
      setSuccess('Game added successfully!');
      setTimeout(() => setSuccess(null), 3000);
    }
    return success;
  };

  const handleRemoveGame = async (id: string) => {
    const success = await onRemoveGame(id);
    if (success) {
      setSuccess('Game removed successfully!');
      setTimeout(() => setSuccess(null), 3000);
    }
    return success;
  };

  return (
    <>
      <div className="content-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1>Game Library</h1>
          <button
            className="btn btn-primary"
            onClick={() => setShowAddForm(!showAddForm)}
          >
            {showAddForm ? 'Cancel' : 'Add Game'}
          </button>
        </div>
      </div>
      <div className="content-body">
        {error && (
          <div className="error">
            {error}
            <button
              style={{ float: 'right', background: 'none', border: 'none', color: 'white', cursor: 'pointer' }}
              onClick={onClearError}
            >
              ×
            </button>
          </div>
        )}
        
        {success && (
          <div className="success">
            {success}
            <button
              style={{ float: 'right', background: 'none', border: 'none', color: 'white', cursor: 'pointer' }}
              onClick={() => setSuccess(null)}
            >
              ×
            </button>
          </div>
        )}

        {showAddForm && (
          <AddGameForm
            onAddGame={handleAddGame}
            onCancel={() => setShowAddForm(false)}
          />
        )}

        {games.length === 0 ? (
          <div className="empty-state">
            <div className="empty-state-icon">🎮</div>
            <h3>No games in your library</h3>
            <p>Add your first game to get started!</p>
          </div>
        ) : (
          <div className="games-grid">
            {games.map((game) => (
              <GameCard
                key={game.id}
                game={game}
                onLaunch={() => onLaunchGame(game.id)}
                onRemove={() => handleRemoveGame(game.id)}
              />
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default GameLibrary;
