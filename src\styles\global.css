* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: #ffffff;
  overflow: hidden;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.title-bar {
  height: 32px;
  background: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-app-region: drag;
  border-bottom: 1px solid #333;
  font-size: 14px;
  font-weight: 500;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 250px;
  background: #2a2a2a;
  border-right: 1px solid #333;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #333;
}

.sidebar-nav {
  flex: 1;
  padding: 10px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
  background: none;
  color: #ccc;
  width: 100%;
  text-align: left;
  font-size: 14px;
}

.nav-item:hover {
  background: #333;
}

.nav-item.active {
  background: #0078d4;
  color: white;
}

.nav-icon {
  margin-right: 10px;
  font-size: 16px;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-header {
  padding: 20px 30px;
  border-bottom: 1px solid #333;
  background: #1e1e1e;
}

.content-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.content-body {
  flex: 1;
  padding: 20px 30px;
  overflow-y: auto;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.game-card {
  background: #2a2a2a;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.game-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.game-image {
  width: 100%;
  height: 160px;
  background: linear-gradient(45deg, #333, #555);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: #666;
}

.game-info {
  padding: 15px;
}

.game-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 5px 0;
  color: #fff;
}

.game-path {
  font-size: 12px;
  color: #999;
  margin: 0 0 10px 0;
  word-break: break-all;
}

.game-actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #0078d4;
  color: white;
}

.btn-primary:hover {
  background: #106ebe;
}

.btn-secondary {
  background: #444;
  color: white;
}

.btn-secondary:hover {
  background: #555;
}

.btn-danger {
  background: #d13438;
  color: white;
}

.btn-danger:hover {
  background: #b02a2e;
}

.add-game-form {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #ccc;
}

.form-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #555;
  border-radius: 4px;
  background: #1a1a1a;
  color: white;
  font-size: 14px;
}

.form-input:focus {
  outline: none;
  border-color: #0078d4;
}

.file-input-group {
  display: flex;
  gap: 10px;
  align-items: end;
}

.file-input-group .form-input {
  flex: 1;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  font-size: 20px;
  color: #ccc;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #999;
}

.error {
  background: #d13438;
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.success {
  background: #107c10;
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}
