import React, { useState, useEffect } from 'react';
import { GameInfo } from '../preload';

interface GameCardProps {
  game: GameInfo;
  onLaunch: () => Promise<boolean>;
  onRemove: () => Promise<boolean>;
}

const GameCard: React.FC<GameCardProps> = ({ game, onLaunch, onRemove }) => {
  const [isLaunching, setIsLaunching] = useState(false);
  const [isRunning, setIsRunning] = useState(false);

  useEffect(() => {
    const checkRunningStatus = async () => {
      try {
        const running = await window.electronAPI.isGameRunning(game.id);
        setIsRunning(running);
      } catch (error) {
        console.error('Error checking game running status:', error);
      }
    };

    checkRunningStatus();
    const interval = setInterval(checkRunningStatus, 2000); // Check every 2 seconds

    return () => clearInterval(interval);
  }, [game.id]);

  const handleLaunch = async () => {
    setIsLaunching(true);
    try {
      await onLaunch();
    } finally {
      setIsLaunching(false);
    }
  };

  const formatLastPlayed = (date?: Date) => {
    if (!date) return 'Never played';
    const now = new Date();
    const played = new Date(date);
    const diffMs = now.getTime() - played.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    return played.toLocaleDateString();
  };

  return (
    <div className="game-card">
      <div className="game-image">
        {game.iconPath ? (
          <img src={game.iconPath} alt={game.name} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
        ) : (
          <span>🎮</span>
        )}
      </div>
      <div className="game-info">
        <h3 className="game-title">{game.name}</h3>
        <p className="game-path">{game.executablePath}</p>
        <div style={{ fontSize: '12px', color: '#999', marginBottom: '10px' }}>
          Last played: {formatLastPlayed(game.lastPlayed)}
        </div>
        <div className="game-actions">
          <button
            className={`btn ${isRunning ? 'btn-secondary' : 'btn-primary'}`}
            onClick={handleLaunch}
            disabled={isLaunching}
          >
            {isLaunching ? 'Launching...' : isRunning ? 'Running' : 'Play'}
          </button>
          <button
            className="btn btn-danger"
            onClick={onRemove}
            title="Remove from library"
          >
            Remove
          </button>
        </div>
      </div>
    </div>
  );
};

export default GameCard;
