import React from 'react';

const Settings: React.FC = () => {
  const handleOpenGitHub = () => {
    window.electronAPI.openExternal('https://github.com');
  };

  return (
    <>
      <div className="content-header">
        <h1>Settings</h1>
      </div>
      <div className="content-body">
        <div style={{ maxWidth: '600px' }}>
          <div className="add-game-form">
            <h3 style={{ margin: '0 0 20px 0', color: '#fff' }}>Application Settings</h3>
            
            <div className="form-group">
              <label className="form-label">Theme</label>
              <select className="form-input" defaultValue="dark">
                <option value="dark">Dark</option>
                <option value="light" disabled>Light (Coming Soon)</option>
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Auto-launch games in fullscreen</label>
              <input type="checkbox" style={{ marginLeft: '10px' }} />
            </div>

            <div className="form-group">
              <label className="form-label">Check for updates on startup</label>
              <input type="checkbox" defaultChecked style={{ marginLeft: '10px' }} />
            </div>

            <div className="form-group">
              <label className="form-label">Minimize to system tray</label>
              <input type="checkbox" style={{ marginLeft: '10px' }} />
            </div>
          </div>

          <div className="add-game-form">
            <h3 style={{ margin: '0 0 20px 0', color: '#fff' }}>About</h3>
            
            <div style={{ color: '#ccc', lineHeight: '1.6' }}>
              <p><strong>RAH Launcher</strong> v1.0.0</p>
              <p>A cross-platform game launcher built with Electron and React.</p>
              <p>
                <button 
                  className="btn btn-primary" 
                  onClick={handleOpenGitHub}
                  style={{ marginTop: '10px' }}
                >
                  View on GitHub
                </button>
              </p>
            </div>
          </div>

          <div className="add-game-form">
            <h3 style={{ margin: '0 0 20px 0', color: '#fff' }}>System Information</h3>
            
            <div style={{ color: '#ccc', fontSize: '14px' }}>
              <p><strong>Platform:</strong> {navigator.platform}</p>
              <p><strong>User Agent:</strong> {navigator.userAgent}</p>
              <p><strong>Language:</strong> {navigator.language}</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Settings;
