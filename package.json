{"name": "rah-launcher", "version": "1.0.0", "description": "Cross-platform game launcher similar to Epic Games Launcher", "main": "dist/main.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx .", "build": "webpack --mode=production", "dev": "webpack --mode=development --watch"}, "keywords": ["electron", "game-launcher", "cross-platform", "react", "typescript"], "author": "Your Name", "license": "MIT", "config": {"forge": {"packagerConfig": {"icon": "./assets/icon"}, "makers": [{"name": "@electron-forge/maker-squirrel", "config": {"name": "rah_launcher"}}, {"name": "@electron-forge/maker-zip", "platforms": ["darwin"]}, {"name": "@electron-forge/maker-deb", "config": {}}, {"name": "@electron-forge/maker-rpm", "config": {}}], "plugins": [{"name": "@electron-forge/plugin-webpack", "config": {"mainConfig": "./webpack.main.config.js", "renderer": {"config": "./webpack.renderer.config.js", "entryPoints": [{"html": "./src/index.html", "js": "./src/renderer.tsx", "name": "main_window", "preload": {"js": "./src/preload.ts"}}]}}}]}}, "devDependencies": {"@electron-forge/cli": "^7.2.0", "@electron-forge/maker-deb": "^7.2.0", "@electron-forge/maker-rpm": "^7.2.0", "@electron-forge/maker-squirrel": "^7.2.0", "@electron-forge/maker-zip": "^7.2.0", "@electron-forge/plugin-webpack": "^7.2.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vercel/webpack-asset-relocator-loader": "^1.7.3", "css-loader": "^6.8.1", "electron": "^28.1.0", "eslint": "^8.56.0", "eslint-plugin-import": "^2.29.1", "fork-ts-checker-webpack-plugin": "^9.0.2", "node-loader": "^2.0.0", "style-loader": "^3.3.3", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "dependencies": {"electron-squirrel-startup": "^1.0.0", "react": "^18.2.0", "react-dom": "^18.2.0"}}