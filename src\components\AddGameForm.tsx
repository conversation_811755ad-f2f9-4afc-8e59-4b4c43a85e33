import React, { useState } from 'react';
import { GameInfo } from '../preload';

interface AddGameFormProps {
  onAddGame: (gameInfo: Omit<GameInfo, 'id'>) => Promise<boolean>;
  onCancel: () => void;
}

const AddGameForm: React.FC<AddGameFormProps> = ({ onAddGame, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    executablePath: '',
    installPath: '',
    iconPath: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectExecutable = async () => {
    try {
      const path = await window.electronAPI.selectExecutable();
      if (path) {
        setFormData(prev => ({ 
          ...prev, 
          executablePath: path,
          // Auto-fill name from executable filename if name is empty
          name: prev.name || path.split(/[/\\]/).pop()?.replace(/\.[^/.]+$/, '') || ''
        }));
      }
    } catch (error) {
      console.error('Error selecting executable:', error);
      setError('Failed to select executable');
    }
  };

  const handleSelectInstallPath = async () => {
    try {
      const path = await window.electronAPI.selectDirectory();
      if (path) {
        setFormData(prev => ({ ...prev, installPath: path }));
      }
    } catch (error) {
      console.error('Error selecting install path:', error);
      setError('Failed to select install path');
    }
  };

  const handleSelectIcon = async () => {
    try {
      const path = await window.electronAPI.selectExecutable(); // Reuse for image files
      if (path) {
        setFormData(prev => ({ ...prev, iconPath: path }));
      }
    } catch (error) {
      console.error('Error selecting icon:', error);
      setError('Failed to select icon');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!formData.name.trim()) {
      setError('Game name is required');
      return;
    }

    if (!formData.executablePath.trim()) {
      setError('Executable path is required');
      return;
    }

    if (!formData.installPath.trim()) {
      setError('Install path is required');
      return;
    }

    setIsSubmitting(true);
    try {
      const gameInfo: Omit<GameInfo, 'id'> = {
        name: formData.name.trim(),
        executablePath: formData.executablePath.trim(),
        installPath: formData.installPath.trim(),
        iconPath: formData.iconPath.trim() || undefined
      };

      const success = await onAddGame(gameInfo);
      if (success) {
        // Form will be closed by parent component
      }
    } catch (error) {
      console.error('Error adding game:', error);
      setError('Failed to add game');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form className="add-game-form" onSubmit={handleSubmit}>
      <h3 style={{ margin: '0 0 20px 0', color: '#fff' }}>Add New Game</h3>
      
      {error && (
        <div className="error" style={{ marginBottom: '15px' }}>
          {error}
        </div>
      )}

      <div className="form-group">
        <label className="form-label">Game Name *</label>
        <input
          type="text"
          name="name"
          value={formData.name}
          onChange={handleInputChange}
          className="form-input"
          placeholder="Enter game name"
          required
        />
      </div>

      <div className="form-group">
        <label className="form-label">Executable Path *</label>
        <div className="file-input-group">
          <input
            type="text"
            name="executablePath"
            value={formData.executablePath}
            onChange={handleInputChange}
            className="form-input"
            placeholder="Path to game executable"
            required
          />
          <button
            type="button"
            className="btn btn-secondary"
            onClick={handleSelectExecutable}
          >
            Browse
          </button>
        </div>
      </div>

      <div className="form-group">
        <label className="form-label">Install Path *</label>
        <div className="file-input-group">
          <input
            type="text"
            name="installPath"
            value={formData.installPath}
            onChange={handleInputChange}
            className="form-input"
            placeholder="Game installation directory"
            required
          />
          <button
            type="button"
            className="btn btn-secondary"
            onClick={handleSelectInstallPath}
          >
            Browse
          </button>
        </div>
      </div>

      <div className="form-group">
        <label className="form-label">Icon Path (Optional)</label>
        <div className="file-input-group">
          <input
            type="text"
            name="iconPath"
            value={formData.iconPath}
            onChange={handleInputChange}
            className="form-input"
            placeholder="Path to game icon"
          />
          <button
            type="button"
            className="btn btn-secondary"
            onClick={handleSelectIcon}
          >
            Browse
          </button>
        </div>
      </div>

      <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end', marginTop: '20px' }}>
        <button
          type="button"
          className="btn btn-secondary"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn btn-primary"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Adding...' : 'Add Game'}
        </button>
      </div>
    </form>
  );
};

export default AddGameForm;
