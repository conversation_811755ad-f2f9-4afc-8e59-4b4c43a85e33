import { contextBridge, ipcRenderer } from 'electron';

export interface GameInfo {
  id: string;
  name: string;
  executablePath: string;
  iconPath?: string;
  installPath: string;
  lastPlayed?: Date;
  playtime?: number;
}

export interface ElectronAPI {
  getGames: () => Promise<GameInfo[]>;
  addGame: (gameInfo: Omit<GameInfo, 'id'>) => Promise<string>;
  removeGame: (id: string) => Promise<boolean>;
  launchGame: (id: string) => Promise<boolean>;
  isGameRunning: (id: string) => Promise<boolean>;
  selectExecutable: () => Promise<string | null>;
  selectDirectory: () => Promise<string | null>;
  openExternal: (url: string) => Promise<void>;
}

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  getGames: () => ipcRenderer.invoke('get-games'),
  addGame: (gameInfo: Omit<GameInfo, 'id'>) => ipcRenderer.invoke('add-game', gameInfo),
  removeGame: (id: string) => ipcRenderer.invoke('remove-game', id),
  launchGame: (id: string) => ipcRenderer.invoke('launch-game', id),
  isGameRunning: (id: string) => ipcRenderer.invoke('is-game-running', id),
  selectExecutable: () => ipcRenderer.invoke('select-executable'),
  selectDirectory: () => ipcRenderer.invoke('select-directory'),
  openExternal: (url: string) => ipcRenderer.invoke('open-external', url),
} as ElectronAPI);

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
