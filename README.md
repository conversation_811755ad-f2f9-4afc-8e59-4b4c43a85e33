# RAH Launcher

A cross-platform game launcher similar to Epic Games Launcher, built with Electron, React, and TypeScript.

## Features

- 🎮 **Game Library Management** - Add, organize, and launch your games
- 🖥️ **Cross-Platform** - Works on Windows, macOS, and Linux
- 🎨 **Modern UI** - Clean, responsive interface with dark theme
- 🚀 **Easy Game Launching** - One-click game launching with process management
- 📊 **Game Tracking** - Track last played times and game status
- ⚙️ **Settings** - Customizable launcher preferences

## Prerequisites

Before you can run this project, you need to install Node.js:

### Installing Node.js

1. **Windows:**
   - Download Node.js from [nodejs.org](https://nodejs.org/)
   - Choose the LTS version (recommended)
   - Run the installer and follow the setup wizard

2. **macOS:**
   - Download from [nodejs.org](https://nodejs.org/) or use Homebrew:
   ```bash
   brew install node
   ```

3. **Linux:**
   ```bash
   # Ubuntu/Debian
   curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # CentOS/RHEL/Fedora
   curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
   sudo yum install -y nodejs
   ```

## Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start the development server:**
   ```bash
   npm start
   ```

## Building for Production

### Package for current platform:
```bash
npm run package
```

### Build distributables for all platforms:
```bash
npm run make
```

This will create installers in the `out/` directory:
- **Windows:** `.exe` installer
- **macOS:** `.dmg` file
- **Linux:** `.deb` and `.rpm` packages

## Usage

### Adding Games

1. Click the "Add Game" button in the Game Library
2. Fill in the game details:
   - **Game Name:** Display name for your game
   - **Executable Path:** Path to the game's .exe file (use Browse button)
   - **Install Path:** Game's installation directory
   - **Icon Path:** Optional custom icon for the game

### Launching Games

- Click the "Play" button on any game card
- The launcher will start the game and track its running status
- Games show "Running" status while active

### Managing Games

- **Remove:** Click the "Remove" button to remove games from your library
- **View Details:** Game cards show last played time and executable path

## Project Structure

```
rah-launcher/
├── src/
│   ├── main.ts              # Electron main process
│   ├── preload.ts           # Preload script for IPC
│   ├── renderer.tsx         # React app entry point
│   ├── components/          # React components
│   │   ├── App.tsx
│   │   ├── Sidebar.tsx
│   │   ├── GameLibrary.tsx
│   │   ├── GameCard.tsx
│   │   ├── AddGameForm.tsx
│   │   └── Settings.tsx
│   └── styles/
│       └── global.css       # Global styles
├── package.json
├── tsconfig.json
├── webpack.*.js             # Webpack configuration
└── README.md
```

## Development

### Available Scripts

- `npm start` - Start development server
- `npm run build` - Build for production
- `npm run package` - Package app for current platform
- `npm run make` - Create distributables
- `npm run lint` - Run ESLint

### Technologies Used

- **Electron** - Desktop app framework
- **React** - UI library
- **TypeScript** - Type-safe JavaScript
- **Webpack** - Module bundler
- **Electron Forge** - Build and packaging tools

## Cross-Platform Compatibility

The launcher is designed to work across different operating systems:

- **Windows:** Supports .exe files and Windows-style paths
- **macOS:** Supports .app bundles and Unix paths
- **Linux:** Supports various executable formats and Unix paths

Game detection and launching automatically adapts to the host operating system.

## Configuration

Game data is stored in:
- **Windows:** `%USERPROFILE%\.rah-launcher\games.json`
- **macOS/Linux:** `~/.rah-launcher/games.json`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test on multiple platforms if possible
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Troubleshooting

### Common Issues

1. **"npm not found" error:**
   - Make sure Node.js is properly installed
   - Restart your terminal/command prompt

2. **Game won't launch:**
   - Verify the executable path is correct
   - Check file permissions
   - Ensure the game doesn't require additional dependencies

3. **Build fails:**
   - Clear node_modules: `rm -rf node_modules && npm install`
   - Update dependencies: `npm update`

### Platform-Specific Notes

- **Windows:** Some antivirus software may flag the packaged app
- **macOS:** You may need to allow the app in Security & Privacy settings
- **Linux:** Ensure execute permissions on the packaged binary

## Future Enhancements

- [ ] Game auto-discovery
- [ ] Steam integration
- [ ] Download/update management
- [ ] Cloud save synchronization
- [ ] Achievement tracking
- [ ] Friends and social features
- [ ] Plugin system
- [ ] Multiple themes
