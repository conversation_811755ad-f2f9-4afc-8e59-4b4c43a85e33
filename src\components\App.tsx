import React, { useState, useEffect } from 'react';
import Sidebar from './Sidebar';
import GameLibrary from './GameLibrary';
import Settings from './Settings';
import { GameInfo } from '../preload';

type View = 'library' | 'settings';

const App: React.FC = () => {
  const [currentView, setCurrentView] = useState<View>('library');
  const [games, setGames] = useState<GameInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadGames();
  }, []);

  const loadGames = async () => {
    try {
      setLoading(true);
      setError(null);
      const gamesList = await window.electronAPI.getGames();
      setGames(gamesList);
    } catch (err) {
      setError('Failed to load games');
      console.error('Error loading games:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddGame = async (gameInfo: Omit<GameInfo, 'id'>) => {
    try {
      await window.electronAPI.addGame(gameInfo);
      await loadGames(); // Refresh the games list
      return true;
    } catch (err) {
      console.error('Error adding game:', err);
      setError('Failed to add game');
      return false;
    }
  };

  const handleRemoveGame = async (id: string) => {
    try {
      await window.electronAPI.removeGame(id);
      await loadGames(); // Refresh the games list
      return true;
    } catch (err) {
      console.error('Error removing game:', err);
      setError('Failed to remove game');
      return false;
    }
  };

  const handleLaunchGame = async (id: string) => {
    try {
      const success = await window.electronAPI.launchGame(id);
      if (!success) {
        setError('Failed to launch game');
      }
      return success;
    } catch (err) {
      console.error('Error launching game:', err);
      setError('Failed to launch game');
      return false;
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div className="loading">
          <div>Loading...</div>
        </div>
      );
    }

    switch (currentView) {
      case 'library':
        return (
          <GameLibrary
            games={games}
            onAddGame={handleAddGame}
            onRemoveGame={handleRemoveGame}
            onLaunchGame={handleLaunchGame}
            error={error}
            onClearError={() => setError(null)}
          />
        );
      case 'settings':
        return <Settings />;
      default:
        return <div>Unknown view</div>;
    }
  };

  return (
    <div className="app">
      <div className="title-bar">
        RAH Launcher
      </div>
      <div className="main-content">
        <Sidebar currentView={currentView} onViewChange={setCurrentView} />
        <div className="content-area">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default App;
