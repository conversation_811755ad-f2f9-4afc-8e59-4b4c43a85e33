import React, { useState } from 'react';

interface FeaturedGame {
  id: string;
  title: string;
  developer: string;
  originalPrice: number;
  salePrice?: number;
  discount?: number;
  image: string;
  description: string;
  tags: string[];
}

const Store: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('featured');

  // Mock featured games data (similar to Epic Games Store)
  const featuredGames: FeaturedGame[] = [
    {
      id: '1',
      title: 'Cyberpunk 2077',
      developer: 'CD PROJEKT RED',
      originalPrice: 59.99,
      salePrice: 29.99,
      discount: 50,
      image: 'https://via.placeholder.com/300x400/0f3460/ffffff?text=Cyberpunk+2077',
      description: 'An open-world, action-adventure RPG set in the megalopolis of Night City.',
      tags: ['RPG', 'Open World', 'Sci-Fi']
    },
    {
      id: '2',
      title: 'The Witcher 3: Wild Hunt',
      developer: 'CD PROJEKT RED',
      originalPrice: 39.99,
      salePrice: 9.99,
      discount: 75,
      image: 'https://via.placeholder.com/300x400/8B4513/ffffff?text=The+Witcher+3',
      description: 'A story-driven open world RPG set in a visually stunning fantasy universe.',
      tags: ['RPG', 'Fantasy', 'Open World']
    },
    {
      id: '3',
      title: 'Fortnite',
      developer: 'Epic Games',
      originalPrice: 0,
      image: 'https://via.placeholder.com/300x400/FF6B35/ffffff?text=Fortnite',
      description: 'Battle Royale game where you fight to be the last one standing.',
      tags: ['Battle Royale', 'Free to Play', 'Multiplayer']
    },
    {
      id: '4',
      title: 'Grand Theft Auto V',
      developer: 'Rockstar Games',
      originalPrice: 29.99,
      salePrice: 14.99,
      discount: 50,
      image: 'https://via.placeholder.com/300x400/2C3E50/ffffff?text=GTA+V',
      description: 'An action-adventure game played from either a third-person or first-person perspective.',
      tags: ['Action', 'Open World', 'Crime']
    },
    {
      id: '5',
      title: 'Rocket League',
      developer: 'Psyonix',
      originalPrice: 0,
      image: 'https://via.placeholder.com/300x400/E74C3C/ffffff?text=Rocket+League',
      description: 'Soccer meets driving in this physics-based multiplayer game.',
      tags: ['Sports', 'Free to Play', 'Multiplayer']
    },
    {
      id: '6',
      title: 'Assassin\'s Creed Valhalla',
      developer: 'Ubisoft',
      originalPrice: 59.99,
      salePrice: 19.99,
      discount: 67,
      image: 'https://via.placeholder.com/300x400/34495E/ffffff?text=AC+Valhalla',
      description: 'Become Eivor, a legendary Viking raider on a quest for glory.',
      tags: ['Action', 'Adventure', 'Historical']
    }
  ];

  const categories = [
    { id: 'featured', name: 'Featured', icon: '⭐' },
    { id: 'free', name: 'Free Games', icon: '🆓' },
    { id: 'sale', name: 'On Sale', icon: '🏷️' },
    { id: 'new', name: 'New Releases', icon: '🆕' },
    { id: 'popular', name: 'Most Popular', icon: '🔥' }
  ];

  const getFilteredGames = () => {
    switch (selectedCategory) {
      case 'free':
        return featuredGames.filter(game => game.originalPrice === 0);
      case 'sale':
        return featuredGames.filter(game => game.salePrice !== undefined);
      case 'featured':
      default:
        return featuredGames;
    }
  };

  const handleGameClick = (game: FeaturedGame) => {
    // In a real store, this would open game details or start download
    alert(`This would open details for ${game.title}. In a real implementation, this would connect to a game distribution service.`);
  };

  return (
    <>
      <div className="content-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1>Store</h1>
          <div style={{ display: 'flex', gap: '10px' }}>
            <input 
              type="text" 
              placeholder="Search games..." 
              className="form-input"
              style={{ width: '300px' }}
            />
          </div>
        </div>
      </div>
      
      <div className="content-body">
        {/* Category Navigation */}
        <div style={{ 
          display: 'flex', 
          gap: '10px', 
          marginBottom: '30px',
          borderBottom: '1px solid #333',
          paddingBottom: '15px'
        }}>
          {categories.map(category => (
            <button
              key={category.id}
              className={`btn ${selectedCategory === category.id ? 'btn-primary' : 'btn-secondary'}`}
              onClick={() => setSelectedCategory(category.id)}
              style={{ display: 'flex', alignItems: 'center', gap: '5px' }}
            >
              <span>{category.icon}</span>
              {category.name}
            </button>
          ))}
        </div>

        {/* Featured Banner */}
        {selectedCategory === 'featured' && (
          <div style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '12px',
            padding: '40px',
            marginBottom: '30px',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{ position: 'relative', zIndex: 2 }}>
              <h2 style={{ margin: '0 0 10px 0', fontSize: '32px', fontWeight: 'bold' }}>
                Weekly Free Game
              </h2>
              <p style={{ margin: '0 0 20px 0', fontSize: '18px', opacity: 0.9 }}>
                Claim your free copy of The Witcher 3: Wild Hunt - Game of the Year Edition
              </p>
              <button className="btn btn-primary" style={{ 
                background: 'white', 
                color: '#333', 
                fontWeight: 'bold',
                padding: '12px 24px'
              }}>
                Get Free Game
              </button>
            </div>
          </div>
        )}

        {/* Games Grid */}
        <div className="games-grid" style={{ gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))' }}>
          {getFilteredGames().map((game) => (
            <div 
              key={game.id} 
              className="game-card"
              onClick={() => handleGameClick(game)}
              style={{ cursor: 'pointer' }}
            >
              <div className="game-image" style={{ 
                backgroundImage: `url(${game.image})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                position: 'relative'
              }}>
                {game.discount && (
                  <div style={{
                    position: 'absolute',
                    top: '10px',
                    right: '10px',
                    background: '#e74c3c',
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}>
                    -{game.discount}%
                  </div>
                )}
              </div>
              <div className="game-info">
                <h3 className="game-title">{game.title}</h3>
                <p style={{ fontSize: '12px', color: '#999', margin: '0 0 5px 0' }}>
                  {game.developer}
                </p>
                <div style={{ 
                  display: 'flex', 
                  flexWrap: 'wrap', 
                  gap: '4px', 
                  marginBottom: '10px' 
                }}>
                  {game.tags.slice(0, 2).map(tag => (
                    <span key={tag} style={{
                      background: '#444',
                      color: '#ccc',
                      padding: '2px 6px',
                      borderRadius: '3px',
                      fontSize: '10px'
                    }}>
                      {tag}
                    </span>
                  ))}
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    {game.originalPrice === 0 ? (
                      <span style={{ color: '#4CAF50', fontWeight: 'bold' }}>FREE</span>
                    ) : (
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        {game.salePrice && (
                          <span style={{ color: '#4CAF50', fontWeight: 'bold' }}>
                            ${game.salePrice}
                          </span>
                        )}
                        <span style={{ 
                          color: game.salePrice ? '#999' : '#fff',
                          textDecoration: game.salePrice ? 'line-through' : 'none',
                          fontSize: game.salePrice ? '12px' : '14px'
                        }}>
                          ${game.originalPrice}
                        </span>
                      </div>
                    )}
                  </div>
                  <button 
                    className="btn btn-primary"
                    style={{ fontSize: '12px', padding: '6px 12px' }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGameClick(game);
                    }}
                  >
                    {game.originalPrice === 0 ? 'Get' : 'Buy'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Store Info */}
        <div style={{ 
          marginTop: '40px', 
          padding: '20px', 
          background: '#2a2a2a', 
          borderRadius: '8px',
          textAlign: 'center',
          color: '#999'
        }}>
          <h3 style={{ color: '#fff', marginBottom: '10px' }}>Demo Store Interface</h3>
          <p>This is a demonstration of a game store interface similar to Epic Games Launcher.</p>
          <p>In a real implementation, this would connect to actual game distribution services.</p>
        </div>
      </div>
    </>
  );
};

export default Store;
