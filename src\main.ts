import { app, BrowserWindow, ipcMain, dialog, shell } from 'electron';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';
import * as fs from 'fs';
import * as os from 'os';

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

interface GameInfo {
  id: string;
  name: string;
  executablePath: string;
  iconPath?: string;
  installPath: string;
  lastPlayed?: Date;
  playtime?: number;
}

class GameLauncher {
  private games: Map<string, GameInfo> = new Map();
  private runningProcesses: Map<string, ChildProcess> = new Map();
  private configPath: string;

  constructor() {
    this.configPath = path.join(os.homedir(), '.rah-launcher', 'games.json');
    this.loadGames();
  }

  private async loadGames(): Promise<void> {
    try {
      if (fs.existsSync(this.configPath)) {
        const data = fs.readFileSync(this.configPath, 'utf8');
        const gamesArray = JSON.parse(data);
        this.games = new Map(gamesArray.map((game: GameInfo) => [game.id, game]));
      }
    } catch (error) {
      console.error('Failed to load games:', error);
    }
  }

  private async saveGames(): Promise<void> {
    try {
      const dir = path.dirname(this.configPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      const gamesArray = Array.from(this.games.values());
      fs.writeFileSync(this.configPath, JSON.stringify(gamesArray, null, 2));
    } catch (error) {
      console.error('Failed to save games:', error);
    }
  }

  async addGame(gameInfo: Omit<GameInfo, 'id'>): Promise<string> {
    const id = Date.now().toString();
    const game: GameInfo = { ...gameInfo, id };
    this.games.set(id, game);
    await this.saveGames();
    return id;
  }

  async removeGame(id: string): Promise<boolean> {
    const removed = this.games.delete(id);
    if (removed) {
      await this.saveGames();
    }
    return removed;
  }

  getGames(): GameInfo[] {
    return Array.from(this.games.values());
  }

  async launchGame(id: string): Promise<boolean> {
    const game = this.games.get(id);
    if (!game) {
      throw new Error(`Game with id ${id} not found`);
    }

    if (!fs.existsSync(game.executablePath)) {
      throw new Error(`Executable not found: ${game.executablePath}`);
    }

    try {
      const process = spawn(game.executablePath, [], {
        cwd: path.dirname(game.executablePath),
        detached: true,
        stdio: 'ignore'
      });

      this.runningProcesses.set(id, process);
      
      // Update last played time
      game.lastPlayed = new Date();
      await this.saveGames();

      process.on('exit', () => {
        this.runningProcesses.delete(id);
      });

      return true;
    } catch (error) {
      console.error(`Failed to launch game ${game.name}:`, error);
      return false;
    }
  }

  isGameRunning(id: string): boolean {
    return this.runningProcesses.has(id);
  }
}

const gameLauncher = new GameLauncher();

const createWindow = (): void => {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    height: 800,
    width: 1200,
    minHeight: 600,
    minWidth: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
    },
    titleBarStyle: 'hidden',
    titleBarOverlay: {
      color: '#1a1a1a',
      symbolColor: '#ffffff'
    }
  });

  // and load the index.html of the app.
  if (MAIN_WINDOW_WEBPACK_ENTRY) {
    mainWindow.loadURL(MAIN_WINDOW_WEBPACK_ENTRY);
  }

  // Open the DevTools in development
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
};

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
app.on('ready', createWindow);

// Quit when all windows are closed, except on macOS.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC handlers for game management
ipcMain.handle('get-games', () => {
  return gameLauncher.getGames();
});

ipcMain.handle('add-game', async (event, gameInfo) => {
  return await gameLauncher.addGame(gameInfo);
});

ipcMain.handle('remove-game', async (event, id) => {
  return await gameLauncher.removeGame(id);
});

ipcMain.handle('launch-game', async (event, id) => {
  try {
    return await gameLauncher.launchGame(id);
  } catch (error) {
    throw error;
  }
});

ipcMain.handle('is-game-running', (event, id) => {
  return gameLauncher.isGameRunning(id);
});

ipcMain.handle('select-executable', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openFile'],
    filters: [
      { name: 'Executable Files', extensions: ['exe', 'app', 'AppImage', 'deb', 'rpm'] }
    ]
  });
  
  return result.canceled ? null : result.filePaths[0];
});

ipcMain.handle('select-directory', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openDirectory']
  });
  
  return result.canceled ? null : result.filePaths[0];
});

ipcMain.handle('open-external', async (event, url) => {
  await shell.openExternal(url);
});

// Global error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

declare const MAIN_WINDOW_WEBPACK_ENTRY: string;
