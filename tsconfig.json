{"compilerOptions": {"allowJs": true, "module": "commonjs", "skipLibCheck": true, "esModuleInterop": true, "noImplicitAny": true, "sourceMap": true, "baseUrl": ".", "outDir": "dist", "moduleResolution": "node", "resolveJsonModule": true, "paths": {"*": ["node_modules/*"]}, "target": "ES2020", "lib": ["ES2020", "DOM"], "jsx": "react-jsx", "strict": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}