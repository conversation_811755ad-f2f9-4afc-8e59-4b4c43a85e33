import React from 'react';

interface SidebarProps {
  currentView: string;
  onViewChange: (view: 'store' | 'library' | 'settings') => void;
}

const Sidebar: React.FC<SidebarProps> = ({ currentView, onViewChange }) => {
  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div style={{
            width: '32px',
            height: '32px',
            background: 'linear-gradient(45deg, #0078d4, #00bcf2)',
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '16px',
            fontWeight: 'bold'
          }}>
            R
          </div>
          <h2 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>RAH Launcher</h2>
        </div>
      </div>
      <nav className="sidebar-nav">
        <button
          className={`nav-item ${currentView === 'store' ? 'active' : ''}`}
          onClick={() => onViewChange('store')}
        >
          <span className="nav-icon">🏪</span>
          Store
        </button>
        <button
          className={`nav-item ${currentView === 'library' ? 'active' : ''}`}
          onClick={() => onViewChange('library')}
        >
          <span className="nav-icon">📚</span>
          Library
        </button>
        <button
          className={`nav-item ${currentView === 'settings' ? 'active' : ''}`}
          onClick={() => onViewChange('settings')}
        >
          <span className="nav-icon">⚙️</span>
          Settings
        </button>
      </nav>
    </div>
  );
};

export default Sidebar;
