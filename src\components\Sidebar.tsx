import React from 'react';

interface SidebarProps {
  currentView: string;
  onViewChange: (view: 'library' | 'settings') => void;
}

const Sidebar: React.FC<SidebarProps> = ({ currentView, onViewChange }) => {
  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h2 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>RAH Launcher</h2>
      </div>
      <nav className="sidebar-nav">
        <button
          className={`nav-item ${currentView === 'library' ? 'active' : ''}`}
          onClick={() => onViewChange('library')}
        >
          <span className="nav-icon">🎮</span>
          Game Library
        </button>
        <button
          className={`nav-item ${currentView === 'settings' ? 'active' : ''}`}
          onClick={() => onViewChange('settings')}
        >
          <span className="nav-icon">⚙️</span>
          Settings
        </button>
      </nav>
    </div>
  );
};

export default Sidebar;
